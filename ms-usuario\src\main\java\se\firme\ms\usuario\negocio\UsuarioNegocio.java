/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.negocio;

import co.venko.ms.models.entity.AdmUsuario;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.EndpointDTO;
import se.firme.commons.firmese.dto.PersonaScanDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.firmese.service.*;
import se.firme.commons.firmese.util.Utilities;
import se.firme.commons.models.projection.IUsuario;
import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.datos.models.dto.FirmanteOrdenDTO;
import se.firme.ms.datos.models.dto.PlantillaRequestDTO;
import se.firme.ms.datos.models.dto.SolicitudFirmaUnificadaDTO;
import se.firme.ms.datos.models.entity.TipoDocumento;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.ServicioService;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.UsuarioServiceImpl;
import se.firme.ms.models.service.ValidacionFuenteServiceImpl;
import se.firme.ms.models.service.helper.UsuarioHelper;
import se.firme.ms.models.service.interfaz.IBitacoraRegistroService;
import se.firme.ms.models.service.interfaz.IParametroService;
import se.firme.ms.usuario.rest.client.UsuarioVMSClient;

/**
 *
 * <AUTHOR>
 */
@Service
public class UsuarioNegocio implements IUsuarioNegosioService {

    @Autowired
    private TokenServiceImpl tokenServiceImpl;

    @Autowired
    private UsuarioServiceImpl usuarioService;

    @Autowired
    private IBitacoraRegistroService bitacoraService;

    @Autowired
    private UsuarioVMSClient usuarioVMSClient;

    @Autowired
    IParametroService parametroService;

    @Autowired
    private ValidacionFuenteServiceImpl validaiconFuenteService;

    @Autowired
    private ServicioService iServicioService;

    @Autowired
    private Environment env;
    @Autowired
    private PasswordEncoder passwordEncoder;

    private static Logger logger = Logger.getLogger(UsuarioNegocio.class.getName());

    @Override
    @Transactional
    public void procesoRegistro(RegistroDTO datosRegistro) throws FirmaException {
        try {
            // verificar si existe el usuario en proceso de registro
            List<Usuario> u = usuarioService.getVerificarUsuarioRegistro(datosRegistro.getNumeroDocumento(),
                    datosRegistro.getCorreoElectronico(), datosRegistro.getNumeroCelular());

            boolean continuarRegistro = true;
            Token token = null;
            Usuario usuario = new Usuario();
            Usuario usuarioCreado = null;
            if (u != null && !u.isEmpty()) {
                usuario = u.get(0);
                usuarioCreado = usuario;

                if (!usuario.getProcesoRegistro()) {
                    continuarRegistro = false;
                } else {
                    token = tokenServiceImpl.getTokenActivoUsuario(usuario);
                }
            } else {
                usuario.setClave(datosRegistro.getContrasena());
                usuario.setNumeroDocumento(datosRegistro.getNumeroDocumento());
                usuario.setFechaExpedicionDocumento(
                        Utilities.getFechaTextoADate(datosRegistro.getFechaExpedicion(), "yyyy-MM-dd"));
                usuario.setCorreoElectronico(datosRegistro.getCorreoElectronico());
                usuario.setNumeroCelular(datosRegistro.getNumeroCelular());
                usuario.setIdTipoDocumento(new TipoDocumento(datosRegistro.getTipoDocumento()));
                usuario.setEstado(true);
                usuarioCreado = usuarioService.crearRegistroUsuario(usuario);

                token = tokenServiceImpl.getTokenActivoUsuario(usuarioCreado);
            }

            if (continuarRegistro) {
                bitacoraService.registrarBitacora(usuarioCreado);
                // token = tokenServiceImpl.getNuevoToken(usuarioCreado,
                // Parameters.string.TOKEN_TIPO_REGISTRO_USUARIO, 30, null);
                if (token != null) {

                    EmailService.send(datosRegistro.getCorreoElectronico(), "Activación de cuenta", EmailTemplateService
                            .registrationEmailTemplate(token.getIdToken(), env.getProperty("app.web.frontend")));
                    validarUsuarioRegistraduria(usuario);
                }
            } else {
                throw new Exception(
                        "El usuario con el correo electrónico o número de documento o número de celular indicado ya se encuentra registrado ");
            }

        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }
    
    // metodo para el autoregistro
    /* 
    @Transactional
    public void procesoAutoRegistro(RegistroDTO datosRegistro) throws FirmaException {
        try { 
            //Proceso nuevo
            //validar entradas


            // verificar si existe el usuario en proceso de registro verificar lo de eliminado, no solo lo de activo
            // verificar si existe el usuario en proceso de registro
            List<Usuario> u = usuarioService.getVerificarUsuarioRegistro(datosRegistro.getNumeroDocumento(),
                    datosRegistro.getCorreoElectronico(), datosRegistro.getNumeroCelular());

            boolean continuarRegistro = true;
            Token token = null;
            Usuario usuario = new Usuario();
            Usuario usuarioCreado = null;
            if (u != null && !u.isEmpty()) {
                usuario = u.get(0);
                usuarioCreado = usuario;
                // *sería if proceso registro y tipo de registro = autoregistro
                if (!usuario.getProcesoRegistro()) {
                    continuarRegistro = false;
                } else {
                    token = tokenServiceImpl.getTokenActivoUsuario(usuario);
                }
            } else {
                usuario.setClave(datosRegistro.getContrasena());
                usuario.setNumeroDocumento(datosRegistro.getNumeroDocumento());
                usuario.setFechaExpedicionDocumento(
                        Utilities.getFechaTextoADate(datosRegistro.getFechaExpedicion(), "yyyy-MM-dd"));
                usuario.setCorreoElectronico(datosRegistro.getCorreoElectronico());
                usuario.setNumeroCelular(datosRegistro.getNumeroCelular());
                usuario.setIdTipoDocumento(new TipoDocumento(datosRegistro.getTipoDocumento()));
                usuario.setEstado(true);
                usuarioCreado = usuarioService.crearRegistroUsuario(usuario);

                token = tokenServiceImpl.getTokenActivoUsuario(usuarioCreado);
            }
            if(continuarRegistro){
                bitacoraService.registrarBitacora(usuarioCreado);
                if (token != null) {
                    //1 crear la solicitud de firma con tyc y tratamiento
                    SolicitudFirmaUnificadaDTO solicitudFirma = crearSolicitudFirmaTyC(usuarioCreado, datosRegistro);
                    //2 delegar a ms firma
                    try{
                        // Comunicación por feign a ms firma devuelve SolicitudFirmaUnificadaDTO, necesita catch solo?
                        ResponseEntity<?> respuestaFirma = firmaMSClient.solicitarFirmaUnificado(solicitudFirma);
                        if (respuestaFirma.getStatusCode().is2xxSuccessful()){
                            Map<String, Object> respuesta = (Map<String, Object>) respuestaFirma.getBody();
                            List<String> tokens = (List<String>) respuesta.get("tokens");
                            if(tokens == null || tokens.isEmpty()){
                                logger.warning("No se generaron tokens de firma");
                                throw new FirmaException("No fue generado el token de firma");
                            }
                            
                            
                            String tokenFirma = tokens.get(0); // Primer token de la lista
                            // 4 enviar el correo con el token de firma
                            if (tokenFirma != null) {
                                // *Debe ser un correo con asunto personalizado cambiar la plantilla
                                EmailService.send(
                                    datosRegistro.getCorreoElectronico(), 
                                    "Completar registro - Firmar TyC", 
                                    EmailTemplateService.getTemplateFirmaMultiple(
                                        tokenFirma, 
                                        env.getProperty("app.web.frontend")
                                    )
                                );
                                logger.info("✅ Correo de firma TyC enviado con token: " + tokenFirma);
                            }  else {
                                logger.warning("⚠️ No se pudo obtener token de firma - enviando token de registro como fallback");
                                // Fallback al token de registro
                                EmailService.send(datosRegistro.getCorreoElectronico(), "Activación de cuenta", 
                                    EmailTemplateService.registrationEmailTemplate(token.getIdToken(), env.getProperty("app.web.frontend")));
                            }


                        } else {
                            logger.warning("⚠️ Error creando proceso de firma - enviando token de registro");
                            throw new FirmaException("Error al generar el proceso de firma");
                        }
                    } catch(Exception e){
                        //devuelve la excepción generada en el ms firma
                        throw new FirmaException(e.getMessage());
                    }
                }
            } else{
                throw new FirmaException(
                        "El usuario con el correo electrónico o número de documento o número de celular indicado ya se encuentra registrado ");
            }

        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }
    */
@Transactional
public void procesoAutoRegistro(RegistroDTO datosRegistro) throws FirmaException {
    try {
        List<Usuario> u = usuarioService.getVerificarUsuarioRegistro(
            datosRegistro.getNumeroDocumento(),
            datosRegistro.getCorreoElectronico(), 
            datosRegistro.getNumeroCelular()
        );

        boolean continuarRegistro = true;
        Usuario usuario = new Usuario();
        Usuario usuarioCreado = null;

        if (u != null && !u.isEmpty()) {
            usuario = u.get(0);
            usuarioCreado = usuario;

            // **MANEJAR VALORES NULL Y DETECTAR AUTOREGISTRO (4)**
            Integer tipoRegistro = usuario.getTipoRegistro();

            // **DETECTAR SI YA ESTÁ EN PROCESO DE AUTOREGISTRO**
            if (usuario.getProcesoRegistro() && 
                (tipoRegistro != null && tipoRegistro.equals(4))) {
                logger.info("🔄 Usuario ya está en proceso de autoregistro - verificando tokens...");
                
                // Intentar reenviar/regenerar token de firma
                manejarReenvioTokenFirma(datosRegistro.getCorreoElectronico());
                return; // Salir sin crear nuevo proceso
                
            } else if (!usuario.getProcesoRegistro()) {
                throw new FirmaException("El usuario ya está registrado y activo en el sistema");
            }
        } else {
            // **NUEVO USUARIO - CREAR CON TIPO AUTOREGISTRO**
            usuario.setClave(datosRegistro.getContrasena());
            usuario.setNumeroDocumento(datosRegistro.getNumeroDocumento());
            usuario.setFechaExpedicionDocumento(
                Utilities.getFechaTextoADate(datosRegistro.getFechaExpedicion(), "yyyy-MM-dd"));
            usuario.setCorreoElectronico(datosRegistro.getCorreoElectronico());
            usuario.setNumeroCelular(datosRegistro.getNumeroCelular());
            usuario.setIdTipoDocumento(new TipoDocumento(datosRegistro.getTipoDocumento()));
            usuario.setEstado(true);
            
            //  MARCAR COMO AUTOREGISTRO
            usuario.setTipoRegistro("AUTOREGISTRO");
            
            // *y si falla? catch
            usuarioCreado = usuarioService.crearRegistroUsuario(usuario);// aqui se le pone el proceso de registro
        }

        // **SOLO CREAR PROCESO DE FIRMA TyC**
            bitacoraService.registrarBitacora(usuarioCreado);
            
            try {
                SolicitudFirmaUnificadaDTO solicitudFirma = crearSolicitudFirmaTyC(usuarioCreado, datosRegistro);
                ResponseEntity<?> respuestaFirma = firmaMSClient.solicitarFirmaUnificado(solicitudFirma);
                
                if (respuestaFirma.getStatusCode().is2xxSuccessful()) {
                    Map<String, Object> respuesta = (Map<String, Object>) respuestaFirma.getBody();
                    List<String> tokens = (List<String>) respuesta.get("tokens");
                    
                    if (tokens != null && !tokens.isEmpty()) {
                        String tokenFirma = tokens.get(0);
                        logger.info("Proceso de autoregistro iniciado - token de firma generado: " + tokenFirma);
                        
                        // El correo se envía automáticamente desde el proceso de firma
                        
                    } else {
                        throw new FirmaException("No se generó token de firma");
                    }
                } else {
                    throw new FirmaException("Error creando proceso de firma TyC");
                }
                
            } catch (Exception e) {
                logger.severe("Error en proceso de autoregistro: " + e.getMessage());
                throw new FirmaException("Error iniciando proceso de firma TyC: " + e.getMessage());
            }
        
    } catch (Exception e) {
        throw new FirmaException(e.getMessage());
    }
}

    /**
 * Crea la solicitud de firma para TyC y autorización de datos
 */
    private SolicitudFirmaUnificadaDTO crearSolicitudFirmaTyC(Usuario usuario, RegistroDTO datosRegistro) {
        SolicitudFirmaUnificadaDTO solicitud = new SolicitudFirmaUnificadaDTO();
        
        // DATOS DEL USUARIO
        solicitud.setIdUsuario(usuario.getIdUsuario());
        
        // PLANTILLAS TyC (configurar IDs según tu sistema)
        List<PlantillaRequestDTO> plantillas = new ArrayList<>();
        
        PlantillaRequestDTO plantillaTyC = new PlantillaRequestDTO();
        plantillaTyC.setIdPlantilla(1L); // ID TyC
        plantillaTyC.setDescripcion("Términos y Condiciones"); 
        plantillas.add(plantillaTyC);
        
        PlantillaRequestDTO plantillaDatos = new PlantillaRequestDTO();
        plantillaDatos.setIdPlantilla(2L); // ID Autorización Datos
        plantillaDatos.setDescripcion("Autorización Datos Personales");
        plantillas.add(plantillaDatos);
        
        solicitud.setPlantillas(plantillas);

        // FECHA DE VIGENCIA (un año desde hoy)
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        solicitud.setFechaVigencia(sdf.format(calendar.getTime()));
        
        // FIRMANTE
        List<FirmanteOrdenDTO> firmantes = new ArrayList<>();
        FirmanteOrdenDTO firmante = new FirmanteOrdenDTO();
        firmante.setEmail(usuario.getCorreoElectronico());
        firmante.setNombreCompleto(usuario.getNombreCompleto());
        firmante.setNumeroCelular(usuario.getNumeroCelular());
        firmante.setRol("Firmante");
        firmante.setOrden(1);
        firmantes.add(firmante);
        
        solicitud.setFirmantes(firmantes);
        
        // CONFIGURACIÓN
        solicitud.setTipoOrden("PARALELO");
        solicitud.setTipoFirma("MULTIPLE");
        solicitud.setDescripcionPersonalizada("Registro de usuario - Firma de TyC");
        
        return solicitud;
    }


    private void validarUsuarioRegistraduria(Usuario usuario) {
        try {
            new Thread(() -> {
                try {
                    EndpointDTO endpointDTO = new EndpointDTO();
                    endpointDTO.setUrl(env.getProperty("routes.custom.konivin.ws"));
                    endpointDTO.setUser(env.getProperty("routes.custom.konivin.pass"));
                    endpointDTO.setPasswd(env.getProperty("routes.custom.konivin.user"));
                    validaiconFuenteService.validarUsuarioEnRegistraduria(usuario, endpointDTO);
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
                }
            }).start();
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
        }
    }

    @Override
    public boolean completarRegistro(DocumentoDTO documento) throws FirmaException {
        try {
            Token token = tokenServiceImpl.consultarCodTransaccion(documento.getCodTransaccion());
            if (token != null) {
                Usuario usuario = token.getIdUsuario();
                if (usuario != null) {
                    logger.log(Level.INFO, "Fecha expedición registro: {0}", usuario.getFechaExpedicionDocumento());
                    String json = Utilities.converB64ToString(documento.getDocument());
                    logger.log(Level.INFO, "Entrante: {0}", json);
                    json = json.substring(json.indexOf("{"), json.indexOf("}") + 1);
                    logger.log(Level.INFO, "Reparado: {0}", json);
                    PersonaScanDTO personaDTO = new Gson().fromJson(json, PersonaScanDTO.class);
                    logger.log(Level.INFO, "Json convertido a objeto");
                    if (personaDTO != null) {
                        logger.log(Level.INFO, "Valida número de cédula: {0}", personaDTO.getDocumentID());
                        logger.log(Level.INFO, "RawData: {0}", Utilities.converB64ToString(documento.getRowData()));
                        String documentoRegistro = "";
                        try {
                            documentoRegistro = String.valueOf(Integer.parseInt(personaDTO.getDocumentID()));
                        } catch (Exception e) {
                            documentoRegistro = personaDTO.getDocumentID();
                        }

                        if (documentoRegistro.equals(usuario.getNumeroDocumento())) {
                            usuario.setEstado(true);
                            usuario.setActivo(true);
                            usuario.setProcesoRegistro(false);
                            usuario.setRowData(documento.getRowData());
                            usuario.setDocumentoPersona(documento.getDocument());
                            usuario.setObervacionVerificacion("Usuario Verificado");
                            if (Utilities.isVacio(usuario.getNombreCompleto())) {
                                usuario.setNombreCompleto(personaDTO.getFirstName() + " " + personaDTO.getSecondName()
                                        + " " + personaDTO.getSurename() + " " + personaDTO.getSecondSurename());
                            }
                            usuarioService.editarRegistro(usuario);
                            logger.log(Level.INFO, "Registro usuario actualizado: {0}", documentoRegistro);
                            AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                            usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                            logger.log(Level.INFO, "User admin activo: {0}", documentoRegistro);
                            tokenServiceImpl.desactivarToken(documento.getCodTransaccion());
                            logger.log(Level.INFO, "Token desactivado: {0}", documentoRegistro);
                            return true;
                        }
                        throw new FirmaException("No coincide el número de documento de la persona");
                    }
                    throw new FirmaException("No fue posible obtener el registro de personaDTO");
                }
                throw new FirmaException("No fue posible obtener el registro de usuario");
            }
            throw new FirmaException("No fue posible obtener el registro de token");
        } catch (FirmaException | NullPointerException | IOException | JsonSyntaxException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Override
    public ServicioDTO getUsuarioServicio(long idUsuario) throws FirmaException {
        try {
            ServicioDTO servicio = iServicioService.findById(idUsuario);
            return servicio;
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Override
    public Object agregarPaqueteServicio(String json) throws FirmaException {
        try {
            JsonElement element = new JsonParser().parse(json);
            if (element != null) {
                JsonObject jsonObject = element.getAsJsonObject();
                if (jsonObject != null) {
                    String sku = jsonObject.get("id_sku").getAsString();
                    if (sku != null && !"".equals(sku)) {
                        if (iServicioService.agregarPaqueteServicio(jsonObject.get("codigo").getAsInt(),
                                jsonObject.get("id_sku").getAsInt())) {
                            return "Paquete agregado";
                        }
                    } else {
                        if (iServicioService.agregarPaqueteServicio(jsonObject.get("codigo").getAsInt(),
                                jsonObject.get("cantidad").getAsInt(), jsonObject.get("vigencia").getAsInt(),
                                jsonObject.get("otros").getAsInt())) {
                            return "Paquete agregado";
                        }
                    }
                    return "No se agregó el paquete";
                }
            }
            throw new FirmaException("No se do convertir el json: " + json);
        } catch (Exception e) {
            throw new FirmaException("Error: " + e.getMessage());
        }
    }

    @Override
    public boolean cambiarContrasena(String json) throws FirmaException {
        try {
            JsonElement element = new JsonParser().parse(json);
            if (element != null) {
                JsonObject jsonObject = element.getAsJsonObject();
                if (jsonObject != null) {
                    String ctoken = jsonObject.get("ctc").getAsString();
                    Token token=tokenServiceImpl.consultarToken(ctoken);
                    Usuario usuario=token.getIdUsuario();
                    
                    if (usuario != null) {
                        String passwd = jsonObject.get("passwd").getAsString();
                        AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                        admUsuario.setClaveUsuario(passwordEncoder.encode(passwd));
                        usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                        tokenServiceImpl.eliminarToken(ctoken);
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            throw new FirmaException("Error al cambiar la contraseña: " + e.getMessage());
        }
    }

    @Override
    public IUsuario consultarUsuario(String id) {
        List<IUsuario> lista = usuarioService.consultarUsuario(id);
        if (lista != null && !lista.isEmpty()) {
            return lista.get(0);
        }
        return null;
    }

    public boolean actualizarServicio(ServicioDTO dto) throws FirmaException {
        try {
            String endpoint = null;

            if (dto.getEndpointCBack() != null && !"".equals(dto.getEndpointCBack().trim())) {
                byte[] array = Base64.getDecoder().decode(dto.getEndpointCBack());
                endpoint = new String(array);
            }

            iServicioService.actualizarServicio(dto.getIdServicio(), endpoint, dto.isEndpointCBackHabilitado());

            iServicioService.actualizarNotificarFirma(dto.getIdServicio(), dto.isNotificarFirma());

            return true;
        } catch (Exception e) {
            throw new FirmaException("Error: " + e.getMessage());
        }
    }

    public boolean eliminarCuenta(int idUsuario) throws FirmaException {
        try {
            System.out.println("Eliminando cuenta de usuario con id: " + idUsuario);
            if (usuarioService.eliminarCuenta(idUsuario)) {
                //	usuarioVMSClient.eliminarUsuarioAdm(idUsuario+"");
                System.out.println("Eliminación de usuario adm: ");
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new FirmaException("No se pudo eliminar usuario: " + e.getMessage());
        }
    }

    public IUsuarioOris findByNumeroDeDocumentoOEmail(String docOrEmail) throws FirmaException {
        try {
            return usuarioService.findByNumeroDocumentoOrCorreoElectronico(docOrEmail);
        } catch (Exception e) {
            e.printStackTrace();
            throw new FirmaException("Ocurrió un error al buscar el usuario");
        }
    }

    public boolean validarEliminacionUsuario(int idUsuario, int idReferido) {
        Usuario usuario = usuarioService.findById(idUsuario);
        if (usuario != null && usuario.getIdReferido() != 0 && usuario.getIdReferido() == (long) idReferido) {
            return true;
        }
        return false;
    }

    public Usuario buscarUsuarioPorId(int idUsuario) {
        return usuarioService.findById(idUsuario);
    }

    /* Métodos comentados porque no están siendo utilizados y deben ser corregidos.
    public List<Usuario> listarUsuariosPorReferidoPaginado(long idReferido, int offset, int tamaño) {
        List<Usuario> todosLosUsuarios = usuarioDao.findByIdReferido(idReferido);
        
        if (todosLosUsuarios == null || todosLosUsuarios.isEmpty()) {
            return null;
        }
        
        int inicio = offset;
        int fin = Math.min(inicio + tamaño, todosLosUsuarios.size());
        
        if (inicio >= todosLosUsuarios.size()) {
            return null;
        }
        
        return todosLosUsuarios.subList(inicio, fin);
    }


    public int contarUsuariosPorReferido(long idReferido) {
        List<Usuario> usuarios = usuarioDao.findByIdReferido(idReferido);
        return usuarios != null ? usuarios.size() : 0;
    }
    */
    public UsuarioServiceImpl getUsuarioService() {
		return usuarioService;
	}
    
}
