package se.firme.ms.firma.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;  // ✅ AGREGAR ESTE IMPORT
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import se.firme.ms.datos.models.entity.PlantillaDocumento;
import se.firme.ms.firma.negocio.FirmaNegocio;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dao.IPlantillaDocumentoRepository;
import se.firme.ms.models.service.FirmaOrdenService;
import se.firme.ms.datos.models.dto.*;
import se.firme.ms.models.service.PlantillaService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

// ✅ AGREGAR LA CLASE ApiResponse
class ApiResponse {
    private boolean success;
    private String message;
    private Object data;
    
    public ApiResponse() {}
    
    public ApiResponse(boolean success, String message, Object data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }
    
    // Getters y setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public Object getData() { return data; }
    public void setData(Object data) { this.data = data; }
}

@RefreshScope
@RestController
@RequestMapping("/manager/plantillas")
public class PlantillaController {

    private static final java.util.logging.Logger logger = java.util.logging.Logger.getLogger(PlantillaController.class.getName());
    
    @Autowired
    private IPlantillaDocumentoRepository plantillaRepository;
    
    @Autowired
    private FirmaOrdenService firmaOrdenService;
    
    @Autowired
    private PlantillaService plantillaService;

    @Autowired
    private FirmaNegocio firmaNegocio;
    
    // Cargar plantilla desde JSON
    @PostMapping("/v1/cargar")
    public ResponseEntity<?> cargarPlantilla(@RequestBody CargarPlantillaDTO request) {
        try {
            PlantillaDocumento plantilla = plantillaService.cargarPlantillaDesdeJson(request);
            return ResponseEntity.ok(plantilla);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error cargando plantilla: " + e.getMessage());
        }
    }
    
    // Cargar plantilla con archivo PDF
    @PostMapping("/v1/cargar-con-archivo")
    public ResponseEntity<?> cargarPlantillaConArchivo(
            @RequestParam("archivo") MultipartFile archivo,
            @RequestParam("nombrePlantilla") String nombrePlantilla,
            @RequestParam("descripcion") String descripcion,
            @RequestParam("tipoDocumento") String tipoDocumento,
            @RequestParam("idUsuarioCreador") Long idUsuarioCreador) {
        try {
            CargarPlantillaDTO request = new CargarPlantillaDTO();
            request.setNombrePlantilla(nombrePlantilla);
            request.setDescripcion(descripcion);
            request.setTipoDocumento(tipoDocumento);
            request.setIdUsuarioCreador(idUsuarioCreador);
            request.setArchivo64(java.util.Base64.getEncoder().encodeToString(archivo.getBytes()));
            request.setNombreArchivo(archivo.getOriginalFilename());
            
            PlantillaDocumento plantilla = plantillaService.cargarPlantillaDesdeJson(request);
            return ResponseEntity.ok(plantilla);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error cargando plantilla con archivo: " + e.getMessage());
        }
    }
    
    // Listar plantillas disponibles
    @PostMapping("/v1/listar")
    public ResponseEntity<?> listarPlantillas() {
        try {
            List<PlantillaDocumento> plantillas = plantillaRepository.findAllActivasOrderByNombre();
            return ResponseEntity.ok(plantillas);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }
    
    // Obtener detalles de una plantilla
    @PostMapping("/v1/detalle")
    public ResponseEntity<?> obtenerPlantilla(@RequestBody Map<String, Long> request) {
        try {
            Long idPlantilla = request.get("idPlantilla");
            if (idPlantilla == null) {
                return ResponseEntity.badRequest().body("Error: idPlantilla es requerido");
            }
            
            PlantillaDocumento plantilla = plantillaRepository.findByIdAndActiva(idPlantilla);
            if (plantilla == null) {
                return ResponseEntity.badRequest().body("Plantilla no encontrada");
            }
            return ResponseEntity.ok(plantilla);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }
    
    @PostMapping("/v1/solicitar-firma")
    public ResponseEntity<?> solicitarFirmaUnificado(
            @RequestBody SolicitudFirmaUnificadaDTO request,
            HttpServletRequest httpRequest) {
        try {
            String ipAddress = httpRequest.getRemoteAddr();
            
            logger.info("=== PROCESANDO SOLICITUD UNIFICADA ===");
            logger.info("📋 Request completo: " + request.toString());
            logger.info("🔍 Tipo detectado: " + request.getTipoUsuarioDetectado());
            
            // ✅ VALIDACIÓN MEJORADA
            String tipoUsuario = request.getTipoUsuarioDetectado();
            if ("CONFLICTO_AMBOS".equals(tipoUsuario)) {
                return ResponseEntity.badRequest().body("Error: Request contiene tanto idUsuario como datos de interesado. Use solo uno.");
            }
            if ("INDEFINIDO".equals(tipoUsuario)) {
                return ResponseEntity.badRequest().body("Error: Request debe contener idUsuario O datos completos de interesado.");
            }
            
            Map<String, Object> resultado;
            
            if ("NO_REGISTRADO".equals(tipoUsuario)) {
                logger.info("🔄 Procesando usuario NO REGISTRADO...");
                validarDatosInteresado(request);
                resultado = firmaOrdenService.procesarSolicitudFirmaInteresado(request, ipAddress);
                resultado.put("tipoUsuario", "NO_REGISTRADO");
                
            } else if ("REGISTRADO".equals(tipoUsuario)) {
                logger.info("👤 Procesando usuario REGISTRADO...");
                resultado = firmaOrdenService.procesarSolicitudUnificada(request, ipAddress);
                resultado.put("tipoUsuario", "REGISTRADO");
                
            } else {
                return ResponseEntity.badRequest().body("Error: Tipo de usuario no válido: " + tipoUsuario);
            }
            
            return ResponseEntity.ok(resultado);
            
        } catch (Exception e) {
            logger.severe("Error procesando solicitud: " + e.getMessage());
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }

    @PostMapping("/v1/solicitar-firma-autoregistro")
    public ResponseEntity<?> solicitarFirmaAutoregistro(
            @RequestBody SolicitudFirmaUnificadaDTO request,
            HttpServletRequest httpRequest) {
        try {
            String ipAddress = httpRequest.getRemoteAddr();
            
            logger.info("=== PROCESANDO SOLICITUD AUTOREGISTRO ===");
            logger.info("📋 Request completo: " + request.toString());
            logger.info("🔍 Tipo detectado: " + request.getTipoUsuarioDetectado());
            
            
            Map<String, Object> resultado;
            logger.info("🔄 Procesando usuario NO REGISTRADO...");
            validarDatosInteresado(request);
            resultado = firmaOrdenService.procesarSolicitudFirmaAutoregistro(request, ipAddress);
            resultado.put("tipoUsuario", "NO_REGISTRADO");
             
            return ResponseEntity.ok(resultado);
            
        } catch (Exception e) {
            logger.severe("Error procesando solicitud: " + e.getMessage());
            return ResponseEntity.badRequest().body("Error: " + e.getMessage());
        }
    }

    @PostMapping(path = "/solicitar-firma")
    public ResponseEntity<ApiResponse> solicitarFirmaPlantilla(@RequestBody SolicitudFirmaPlantillaDTO request, HttpServletRequest httpRequest) {
        try {
            // Obtener IP
            String ipAddress = httpRequest.getRemoteAddr();
            if (ipAddress == null || ipAddress.trim().isEmpty()) {
                ipAddress = "127.0.0.1";
            }
            
            logger.info("=== SOLICITAR FIRMA PLANTILLA CON REGISTRO AUTOMÁTICO ===");
            logger.info("ID Plantilla: " + request.getIdPlantilla());
            logger.info("Firmantes: " + (request.getFirmantes() != null ? request.getFirmantes().size() : 0));
            
            // 🎯 **AQUÍ ESTÁ LA CLAVE**: Procesar firmantes mixtos ANTES de crear la solicitud
            if (request.getFirmantes() != null && !request.getFirmantes().isEmpty()) {
                logger.info("🔄 Procesando firmantes mixtos para plantilla...");
                firmaOrdenService.procesarFirmantesMixtos(request.getFirmantes(), request.getIdUsuario());
            }
            
            // Procesar la solicitud con plantilla (método existente)
            Map<String, Object> resultado = firmaOrdenService.procesarSolicitudConPlantilla(request, ipAddress);
            
            ApiResponse response = new ApiResponse();
            response.setSuccess(true);
            response.setMessage("Solicitud de firma con plantilla procesada exitosamente");
            response.setData(resultado);
            
            return ResponseEntity.ok(response);
            
        } catch (FirmaException e) {
            logger.severe("Error procesando solicitud de plantilla: " + e.getMessage());
            return ResponseEntity.badRequest().body(new ApiResponse(false, e.getMessage(), null));
        } catch (Exception e) {
            logger.severe("Error inesperado en plantilla: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                               .body(new ApiResponse(false, "Error interno del servidor", null));
        }
    }

    @PostMapping("/v1/consultar-estado-solicitud")
    public ResponseEntity<?> consultarEstadoSolicitud(@RequestBody Map<String, Long> request, HttpServletRequest httpRequest) {
        Long idArchivoFirma = request.get("idArchivoFirma");
        if (idArchivoFirma == null) {
            return ResponseEntity.badRequest().body("ID de archivo es requerido");
        }

        Long idUsuario = null;
        String emailUsuario = null;

        // Intentar obtener el idUsuario del header
        try {
            idUsuario = se.firme.ms.firma.Utils.UsuarioHeaderUtil.obtenerIdUsuario(httpRequest);
        } catch (IllegalArgumentException ignored) {
            // Si no hay idUsuario, se deja como null
        }

        // Intentar obtener el email del header
        try {
            emailUsuario = se.firme.ms.firma.Utils.UsuarioHeaderUtil.obtenerEmailUsuario(httpRequest);
        } catch (IllegalArgumentException ignored) {
            // Si no hay email, lo deja como null
        }

        try {
            Map<String, Object> estado = firmaNegocio.consultarEstadoSolicitudPorUsuarioOEmail(idArchivoFirma, idUsuario, emailUsuario);
            return ResponseEntity.ok(estado);
        } catch (FirmaException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error: " + e.getMessage());
        }
    }

    /**
     * Valida datos específicos para usuario interesado - REUTILIZAR VALIDACIÓN EXISTENTE
     */
    private void validarDatosInteresado(SolicitudFirmaUnificadaDTO request) throws Exception {
        // Validaciones básicas obligatorias
        if (request.getNombreInteresado() == null || request.getNombreInteresado().trim().isEmpty()) {
            throw new Exception("Nombre del interesado es requerido para usuarios no registrados");
        }
        
        if (request.getEmailInteresado() == null || request.getEmailInteresado().trim().isEmpty()) {
            throw new Exception("Email del interesado es requerido para usuarios no registrados");
        }
        
        if (!request.getEmailInteresado().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new Exception("Formato de email inválido para interesado");
        }
        
        if (request.getTelefonoInteresado() == null || request.getTelefonoInteresado().trim().isEmpty()) {
            throw new Exception("Teléfono del interesado es requerido para usuarios no registrados");
        }
        
        if (request.getTipoDocumentoInteresado() == null || request.getTipoDocumentoInteresado().trim().isEmpty()) {
            throw new Exception("Tipo de documento del interesado es requerido para usuarios no registrados");
        }
        
        if (request.getNumeroDocumentoInteresado() == null || request.getNumeroDocumentoInteresado().trim().isEmpty()) {
            throw new Exception("Número de documento del interesado es requerido para usuarios no registrados");
        }
        
        logger.info("✅ Validación de datos de interesado completada exitosamente");
    }
}
